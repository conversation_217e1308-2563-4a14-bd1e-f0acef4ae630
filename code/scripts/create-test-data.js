const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

// 由于这是 JS 文件，我们需要手动定义当前的模板配置
const CURRENT_TEMPLATE = {
  templateId: "QqgtNhUWYG8EsxArAfAJN-ISRDj5ak5URR7ZPAuymCM",
  content: {
    thing14: "还款项目",
    amount3: "还款金额",
    time4: "还款时间",
  },
};

async function createTestData() {
  try {
    console.log("创建测试数据...");

    // 创建测试用户
    const user = await prisma.user.upsert({
      where: { username: "admin" },
      update: {},
      create: {
        username: "admin",
        password: "$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
      },
    });
    console.log("创建测试用户:", user.username);

    // 创建测试模板
    const template = await prisma.template.upsert({
      where: { templateId: CURRENT_TEMPLATE.templateId },
      update: {},
      create: {
        templateId: CURRENT_TEMPLATE.templateId,
        name: "客户还款提醒",
        variables: JSON.stringify(CURRENT_TEMPLATE.content),
        isActive: true,
      },
    });
    console.log("创建测试模板:", template.name);

    // 创建测试二维码
    const qrcode = await prisma.qRCode.upsert({
      where: { sceneStr: "test_project_15_1000" },
      update: {},
      create: {
        name: "测试项目-15日-1000元",
        sceneStr: "test_project_15_1000",
        templateId: template.id,
        templateData: JSON.stringify({
          thing14: { value: "测试项目" },
          amount3: { value: "1000" },
          time4: { value: "2024年5月15日" },
        }),
        firstNotifyDate: new Date("2024-05-25"),
        monthlyNotifyDay: 15,
        isActive: true,
      },
    });
    console.log("创建测试二维码:", qrcode.name);

    // 创建测试订阅者
    const subscriber = await prisma.subscriber.upsert({
      where: {
        openid_qrcodeId: {
          openid: "test_openid_123",
          qrcodeId: qrcode.id,
        },
      },
      update: {},
      create: {
        openid: "test_openid_123",
        qrcodeId: qrcode.id,
        isActive: true,
      },
    });
    console.log("创建测试订阅者:", subscriber.openid);

    // 生成通知计划
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const schedules = [];

    // 添加今天的通知（用于测试）
    schedules.push({
      subscriberId: subscriber.id,
      scheduledDate: new Date(today),
    });

    // 添加未来几个月的通知
    for (let i = 1; i <= 6; i++) {
      const futureDate = new Date(today);
      futureDate.setMonth(futureDate.getMonth() + i);
      futureDate.setDate(15); // 每月15日

      schedules.push({
        subscriberId: subscriber.id,
        scheduledDate: futureDate,
      });
    }

    // 批量创建通知计划
    for (const schedule of schedules) {
      await prisma.scheduledNotification.upsert({
        where: {
          subscriberId_scheduledDate: {
            subscriberId: schedule.subscriberId,
            scheduledDate: schedule.scheduledDate,
          },
        },
        update: {},
        create: schedule,
      });
    }

    console.log(`创建了 ${schedules.length} 个通知计划`);

    // 创建一些测试通知日志
    await prisma.notificationLog.create({
      data: {
        subscriberId: subscriber.id,
        status: "success",
      },
    });

    console.log("创建测试通知日志");

    console.log("\n测试数据创建完成！");
    console.log("你现在可以访问以下页面进行测试:");
    console.log(`- 订阅者列表: http://localhost:3001/subscribers`);
    console.log(`- 订阅者详情: http://localhost:3001/subscribers/${subscriber.id}`);
    console.log(`- 二维码管理: http://localhost:3001/qrcodes`);
  } catch (error) {
    console.error("创建测试数据失败:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
