"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import QRCodeDisplay from "@/components/QRCodeDisplay";
import QRCodeForm from "@/components/QRCodeForm";
import {
  WECHAT_TEMPLATE,
  calculateReminderDate,
  formatDate,
  generateQRCodeName,
  STORAGE_KEYS,
  generateTemplateData,
  parseTemplateData
} from "@/lib/config";

interface Template {
  id: number;
  templateId: string;
  name: string;
  alias?: string;
  description?: string;
  variables: string;
  isActive: boolean;
}

interface QRCode {
  id: number;
  name: string;
  sceneStr: string;
  templateId: number;
  template: Template;
  templateData: string;
  firstNotifyDate: string;
  monthlyNotifyDay: number;
  isActive: boolean;
  endDate?: string | null;
  subscribers: any[];
}

export default function QRCodeEditPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [qrcode, setQRCode] = useState<QRCode | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");
  const [qrImageUrl, setQrImageUrl] = useState("");
  const [qrImageBase64, setQrImageBase64] = useState("");
  const [qrExpired, setQrExpired] = useState(false);

  // 表单数据状态
  const [formData, setFormData] = useState({
    projectName: "",
    amount: "",
    paymentDay: new Date().getDate(),
    advanceDays: WECHAT_TEMPLATE.defaultAdvanceDays,
    monthsCount: 1
  });

  useEffect(() => {
    async function fetchData() {
      try {
        const token = localStorage.getItem("token");
        const qrRes = await fetch(`/api/qrcodes/${id}`, {
          headers: { Authorization: "Bearer " + token }
        });

        if (!qrRes.ok) {
          setError("获取数据失败");
          setLoading(false);
          return;
        }

        const qrData = await qrRes.json();
        const qrcodeData = qrData.qrcode;

        // 设置二维码数据
        setQRCode({
          ...qrcodeData,
          firstNotifyDate: qrcodeData.firstNotifyDate.slice(0, 10),
          endDate: qrcodeData.endDate ? qrcodeData.endDate.slice(0, 10) : "",
        });

        // 如果有二维码图片URL，设置它
        if (qrcodeData.qrImgUrl) {
          setQrImageUrl(qrcodeData.qrImgUrl);
          if (qrcodeData.qrImgBase64) {
            setQrImageBase64(qrcodeData.qrImgBase64);
          }

          // 检查二维码是否过期或即将过期
          if (qrcodeData.qrGeneratedAt) {
            const generatedAt = new Date(qrcodeData.qrGeneratedAt);
            const now = new Date();
            const daysDiff = Math.floor((now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60 * 24));

            // 如果超过29天，标记为过期
            if (daysDiff >= 29) {
              setQrExpired(true);
              setMessage("二维码已过期，请点击'生成二维码'按钮重新生成");
            }
            // 如果超过25天但不到29天，提示即将过期
            else if (daysDiff >= 25) {
              setMessage(`二维码将在${29 - daysDiff}天后过期，建议重新生成`);
            }
          }
        }

        // 解析模板数据并设置表单数据
        let projectName = "";
        let amount = "";
        if (qrcodeData.templateData) {
          const parsedData = parseTemplateData(qrcodeData.templateData);
          projectName = parsedData.projectName;
          amount = parsedData.amount;
        }

        // 设置表单数据
        setFormData({
          projectName,
          amount,
          paymentDay: qrcodeData.monthlyNotifyDay,
          advanceDays: WECHAT_TEMPLATE.defaultAdvanceDays,
          monthsCount: 12 // 默认12个月
        });
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  const handleSave = async (data: {
    projectName: string;
    paymentDay: number;
    amount: string;
    advanceDays: number;
    monthsCount: number;
  }) => {
    if (!qrcode) return;
    setError("");
    setSaving(true);

    const { projectName, paymentDay, amount, advanceDays, monthsCount } = data;

    if (!projectName || !amount || paymentDay < 1 || paymentDay > 28) {
      setError("请填写所有必填项，还款日必须在1-28之间");
      setSaving(false);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const reminderDate = calculateReminderDate(paymentDay, advanceDays);

      // 保存当前值到本地存储
      localStorage.setItem(STORAGE_KEYS.LAST_PROJECT_NAME, projectName);
      localStorage.setItem(STORAGE_KEYS.LAST_AMOUNT, amount);
      localStorage.setItem(STORAGE_KEYS.LAST_MONTHS_COUNT, monthsCount.toString());
      localStorage.setItem(STORAGE_KEYS.LAST_ADVANCE_DAYS, advanceDays.toString());

      // 生成新的名称（基于当前项目名称、还款日和金额）
      const newName = generateQRCodeName(projectName, paymentDay, amount);

      // 生成模板数据
      const templateData = JSON.stringify(generateTemplateData(
        projectName,
        new Date(reminderDate).toISOString().split('T')[0],
        amount
      ));

      const res = await fetch(`/api/qrcodes/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          name: newName, // 使用新生成的名称
          monthlyNotifyDay: paymentDay,
          firstNotifyDate: formatDate(reminderDate),
          templateData,
          isActive: qrcode.isActive,
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "保存失败");
        setSaving(false);
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("确认删除该二维码？此操作不可撤销")) return;
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/qrcodes/${id}`, {
        method: "DELETE",
        headers: { Authorization: "Bearer " + token },
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "删除失败");
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
    }
  };

  const handleGenerateQRCode = async () => {
    if (!qrcode) return;
    setError("");
    try {
      const token = localStorage.getItem("token");
      const res = await fetch("/api/qrcodes/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          sceneStr: qrcode.sceneStr,
          qrcodeId: qrcode.id
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "生成二维码失败");
        return;
      }
      const data = await res.json();

      // 重置过期状态
      setQrExpired(false);

      // 设置二维码图片URL和base64数据
      setQrImageUrl(data.qr.imgUrl);
      if (data.qr.imgBase64) {
        setQrImageBase64(data.qr.imgBase64);
      }

      // 显示是否重新生成的消息
      if (data.regenerated) {
        setMessage("二维码已重新生成");
      } else {
        setMessage("使用已有的二维码");
      }
    } catch {
      setError("网络错误");
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600 mb-4">{error}</div>;
  if (!qrcode) return null;

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow space-y-4">
      <h2 className="text-2xl font-bold">编辑租金提醒</h2>

      <QRCodeForm
        initialData={formData}
        onSubmit={handleSave}
        submitButtonText="保存"
        submitting={saving}
        error={error}
        showQRCodeInfo={true}
        qrCodeInfo={{
          name: qrcode.name,
          sceneStr: qrcode.sceneStr
        }}
      />

      <div className="flex space-x-4 mt-4">
        <button
          onClick={handleDelete}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          删除
        </button>
        <button
          onClick={handleGenerateQRCode}
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
        >
          生成二维码
        </button>
      </div>
      {message && (
        <div className="p-3 bg-blue-50 rounded">
          <p>{message}</p>
        </div>
      )}
      {qrImageUrl && (
        <div>
          <h3 className="mt-4 font-semibold">二维码预览</h3>
          <div className="relative">
            <QRCodeDisplay
              src={qrImageUrl}
              base64={qrImageBase64}
              alt="二维码"
              size={192}
              filename={`qrcode-${qrcode.name}`}
              className="mt-2"
            />
            {qrExpired && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-500 bg-opacity-50 text-white font-bold">
                已过期
              </div>
            )}
          </div>
        </div>
      )}
    </main>
  );
}
