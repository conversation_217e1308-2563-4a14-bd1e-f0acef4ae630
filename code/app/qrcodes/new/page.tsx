"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import QRCodeForm from "@/components/QRCodeForm";
import {
  calculateReminderDate,
  formatDate,
  generateSceneStr,
  generateQRCodeName,
  STORAGE_KEYS
} from "@/lib/config";

export default function QRCodeNewPage() {
  const router = useRouter();
  const [error, setError] = useState("");
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (data: {
    projectName: string;
    paymentDay: number;
    amount: string;
    advanceDays: number;
    monthsCount: number;
  }) => {
    setError("");
    setSubmitting(true);

    const { projectName, paymentDay, amount, advanceDays, monthsCount } = data;

    if (!projectName || !amount || paymentDay < 1) {
      setError("请填写所有必填项");
      setSubmitting(false);
      return;
    }

    // 保存当前值到本地存储
    localStorage.setItem(STORAGE_KEYS.LAST_PROJECT_NAME, projectName);
    localStorage.setItem(STORAGE_KEYS.LAST_AMOUNT, amount);
    localStorage.setItem(STORAGE_KEYS.LAST_MONTHS_COUNT, monthsCount.toString());
    localStorage.setItem(STORAGE_KEYS.LAST_ADVANCE_DAYS, advanceDays.toString());

    try {
      const token = localStorage.getItem("token");
      const reminderDate = calculateReminderDate(paymentDay, advanceDays);
      const sceneStr = generateSceneStr(projectName, paymentDay, amount);
      const name = generateQRCodeName(projectName, paymentDay, amount);

      const res = await fetch("/api/qrcodes", {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({
          name,
          sceneStr,
          projectName,
          paymentDay,
          amount,
          advanceDays,
          notifyCount: monthsCount,
          firstNotifyDate: formatDate(reminderDate),
        }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "创建失败");
        setSubmitting(false);
        return;
      }
      router.push("/qrcodes");
    } catch {
      setError("网络错误");
      setSubmitting(false);
    }
  };

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow">
      <h2 className="text-2xl font-bold mb-6">新建租金提醒</h2>
      <QRCodeForm
        onSubmit={handleSubmit}
        submitButtonText="创建"
        submitting={submitting}
        error={error}
      />
    </main>
  );
}
