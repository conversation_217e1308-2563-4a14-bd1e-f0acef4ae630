import type { NextApiRequest, NextApiResponse } from "next";
import { prisma } from "@/lib/prisma";
import { WeChatAPI } from "@/lib/wechat";
import { WECHAT_TEMPLATE, generateTemplateData, getNextPaymentDate, formatChineseDate, parseTemplateData } from "@/lib/config";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  const key = req.headers["x-scheduler-key"];
  if (!key || key !== process.env.SCHEDULER_SECRET_KEY) {
    return res.status(401).json({ error: "无权访问" });
  }

  // 获取今天的日期（0点）
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // 查找今天需要发送的通知
  const scheduledNotifications = await prisma.scheduledNotification.findMany({
    where: {
      scheduledDate: {
        gte: today,
        lt: tomorrow,
      },
      status: "pending",
    },
    include: {
      subscriber: {
        include: {
          qrcode: true,
        },
      },
    },
  });

  let sent = 0, failed = 0, logs: any[] = [];

  for (const notification of scheduledNotifications) {
    const { subscriber } = notification;

    // 检查订阅者是否仍然活跃
    if (!subscriber.isActive) {
      // 取消不活跃订阅者的通知
      await prisma.scheduledNotification.update({
        where: { id: notification.id },
        data: { status: "cancelled" },
      });
      continue;
    }

    try {
      // 解析二维码的模板数据以获取项目信息
      const parsedData = parseTemplateData(subscriber.qrcode.templateData);

      // 计算下一期还款日期
      const nextPaymentDate = getNextPaymentDate(subscriber.qrcode.monthlyNotifyDay);
      const formattedPaymentDate = formatChineseDate(nextPaymentDate);

      // 生成模板数据
      const messageTemplateData = generateTemplateData(
        parsedData.projectName,
        formattedPaymentDate,
        parsedData.amount
      );

      // 发送模板消息
      await WeChatAPI.sendTemplateMessage(
        subscriber.openid,
        WECHAT_TEMPLATE.templateId,
        messageTemplateData
      );

      // 更新通知状态
      await prisma.scheduledNotification.update({
        where: { id: notification.id },
        data: {
          status: "sent",
          actualSentAt: new Date(),
        },
      });

      // 记录通知日志
      await prisma.notificationLog.create({
        data: {
          subscriberId: subscriber.id,
          status: "success",
        },
      });

      sent++;
    } catch (e: any) {
      // 更新通知状态为失败
      await prisma.scheduledNotification.update({
        where: { id: notification.id },
        data: {
          status: "failed",
          error: e.message,
        },
      });

      // 记录失败日志
      await prisma.notificationLog.create({
        data: {
          subscriberId: subscriber.id,
          status: "fail",
          error: e.message,
        },
      });

      failed++;
      logs.push({ openid: subscriber.openid, error: e.message });
    }
  }

  res.status(200).json({ sent, failed, logs, total: scheduledNotifications.length });
}
