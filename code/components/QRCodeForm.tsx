"use client";

import { useState, useEffect } from "react";
import {
  WECHAT_TEMPLATE,
  calculateReminderDate,
  formatDate,
  formatChineseDate,
  calculateFutureReminderDates,
  getNextPaymentDate,
  STORAGE_KEYS,
  getTemplateFieldLabels
} from "@/lib/config";

interface QRCodeFormProps {
  initialData?: {
    projectName?: string;
    paymentDay?: number;
    amount?: string;
    advanceDays?: number;
    monthsCount?: number;
  };
  onSubmit: (data: {
    projectName: string;
    paymentDay: number;
    amount: string;
    advanceDays: number;
    monthsCount: number;
  }) => Promise<void>;
  submitButtonText: string;
  submitting: boolean;
  error: string;
  showQRCodeInfo?: boolean;
  qrCodeInfo?: {
    name: string;
    sceneStr: string;
  };
}

export default function QRCodeForm({
  initialData,
  onSubmit,
  submitButtonText,
  submitting,
  error,
  showQRCodeInfo = false,
  qrCodeInfo
}: QRCodeFormProps) {
  const [projectName, setProjectName] = useState(initialData?.projectName || "");
  const [paymentDay, setPaymentDay] = useState(initialData?.paymentDay || new Date().getDate());
  const [amount, setAmount] = useState(initialData?.amount || "");
  const [advanceDays, setAdvanceDays] = useState(initialData?.advanceDays || WECHAT_TEMPLATE.defaultAdvanceDays);
  const [monthsCount, setMonthsCount] = useState(initialData?.monthsCount || 1);

  // 从本地存储加载上次的值（仅在没有初始数据时）
  useEffect(() => {
    if (!initialData) {
      const savedProjectName = localStorage.getItem(STORAGE_KEYS.LAST_PROJECT_NAME);
      const savedAmount = localStorage.getItem(STORAGE_KEYS.LAST_AMOUNT);
      const savedMonthsCount = localStorage.getItem(STORAGE_KEYS.LAST_MONTHS_COUNT);
      const savedAdvanceDays = localStorage.getItem(STORAGE_KEYS.LAST_ADVANCE_DAYS);

      if (savedProjectName) {
        setProjectName(savedProjectName);
      }

      if (savedAmount) {
        setAmount(savedAmount);
      }

      if (savedMonthsCount) {
        setMonthsCount(parseInt(savedMonthsCount));
      }

      if (savedAdvanceDays) {
        setAdvanceDays(parseInt(savedAdvanceDays));
      }
    }
  }, [initialData]);

  // 当初始数据变化时更新表单
  useEffect(() => {
    if (initialData) {
      setProjectName(initialData.projectName || "");
      setPaymentDay(initialData.paymentDay || new Date().getDate());
      setAmount(initialData.amount || "");
      setAdvanceDays(initialData.advanceDays || WECHAT_TEMPLATE.defaultAdvanceDays);
      setMonthsCount(initialData.monthsCount || 1);
    }
  }, [initialData]);

  // 计算未来几个月的提醒日期
  const futureReminderDates = paymentDay > 0 && monthsCount > 0
    ? calculateFutureReminderDates(paymentDay, monthsCount, advanceDays)
    : [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit({
      projectName,
      paymentDay,
      amount,
      advanceDays,
      monthsCount
    });
  };

  // 获取下一次还款日期用于预览
  const nextPaymentDate = paymentDay > 0 ? getNextPaymentDate(paymentDay) : null;
  const formattedNextPaymentDate = nextPaymentDate ? formatChineseDate(nextPaymentDate) : '';

  return (
    <div>
      {error && <div className="mb-4 text-red-600 font-semibold">{error}</div>}
      
      {showQRCodeInfo && qrCodeInfo && (
        <div className="p-3 bg-gray-50 rounded mb-4">
          <h3 className="font-medium mb-2">二维码信息</h3>
          <p><strong>名称:</strong> {qrCodeInfo.name}</p>
          <p><strong>场景字符串:</strong> {qrCodeInfo.sceneStr}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block mb-1 font-medium">{(() => {
            const fieldLabels = getTemplateFieldLabels();
            const fieldNames = Object.keys(fieldLabels) as (keyof typeof fieldLabels)[];
            return fieldLabels[fieldNames[0]];
          })()}</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            required
          />
          <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
        </div>
        
        <div>
          <label className="block mb-1 font-medium">每月还款日</label>
          <input
            type="number"
            min={1}
            max={31}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={paymentDay}
            onChange={(e) => setPaymentDay(Number(e.target.value))}
            required
          />
        </div>
        
        <div>
          <label className="block mb-1 font-medium">{(() => {
            const fieldLabels = getTemplateFieldLabels();
            const fieldNames = Object.keys(fieldLabels) as (keyof typeof fieldLabels)[];
            return fieldLabels[fieldNames[1]];
          })()}</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            required
          />
          <p className="text-sm text-gray-500 mt-1">将显示在微信通知中</p>
        </div>
        
        <div>
          <label className="block mb-1 font-medium">提前提醒天数</label>
          <input
            type="number"
            min={1}
            max={99}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={advanceDays}
            onChange={(e) => setAdvanceDays(Number(e.target.value))}
            required
          />
        </div>
        
        <div>
          <label className="block mb-1 font-medium">发送月数</label>
          <input
            type="number"
            min={1}
            max={24}
            className="w-full border border-gray-300 rounded px-3 py-2"
            value={monthsCount}
            onChange={(e) => setMonthsCount(Number(e.target.value))}
            required
          />
          <p className="text-sm text-gray-500 mt-1">每月发送一次，共发送几个月</p>
        </div>

        {futureReminderDates.length > 0 && (
          <div className="p-3 bg-green-50 rounded mt-2">
            <h3 className="font-medium mb-2">未来{monthsCount}个月的提醒日期</h3>
            <ul className="list-disc pl-5">
              {futureReminderDates.map((date, index) => (
                <li key={index}>{formatDate(date)}</li>
              ))}
            </ul>
          </div>
        )}

        {!showQRCodeInfo && (
          <div className="p-3 bg-gray-50 rounded">
            <h3 className="font-medium mb-2">二维码信息</h3>
            <p><strong>名称:</strong> {projectName ? `${projectName}-${paymentDay}号-${amount}元` : ''}</p>
            <p><strong>场景字符串:</strong> {projectName ? `${projectName}_${paymentDay}_${amount}` : ''}</p>
          </div>
        )}

        <div className="p-3 bg-yellow-50 rounded">
          <h3 className="font-medium mb-2">消息预览</h3>
          <div className="border border-gray-200 p-4 rounded bg-white">
            <div className="font-bold text-lg mb-2">{WECHAT_TEMPLATE.title}</div>
            <div className="space-y-2">
              {(() => {
                const fieldLabels = getTemplateFieldLabels();
                const fieldNames = Object.keys(fieldLabels) as (keyof typeof fieldLabels)[];
                return (
                  <>
                    <div>
                      <span className="text-gray-500">{fieldLabels[fieldNames[0]]}：</span>
                      <span>{projectName || '(未填写)'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">{fieldLabels[fieldNames[1]]}：</span>
                      <span>{amount || '(未填写)'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">{fieldLabels[fieldNames[2]]}：</span>
                      <span>{formattedNextPaymentDate || '(未填写)'}</span>
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
        </div>

        <button
          type="submit"
          disabled={submitting}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {submitting ? `${submitButtonText}中...` : submitButtonText}
        </button>
      </form>
    </div>
  );
}
